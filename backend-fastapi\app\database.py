import requests
import json
from typing import Dict, List, Any, Optional
import logging
from app.config import settings

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.base_url = f"http://{settings.db_host}:{settings.db_port}"
        self.auth = (settings.db_user, settings.db_password)
        self.database = settings.db_name

    def _execute_clickhouse_query(self, query: str, params: Optional[List[Any]] = None) -> requests.Response:
        """Execute a query against ClickHouse HTTP interface"""
        # Replace PostgreSQL-style parameters (%s) with ClickHouse format if params exist
        if params:
            # For ClickHouse, we'll use simple string formatting for now
            # In production, you might want to use proper parameter binding
            formatted_query = query
            for param in params:
                if isinstance(param, str):
                    formatted_query = formatted_query.replace('%s', f"'{param}'", 1)
                else:
                    formatted_query = formatted_query.replace('%s', str(param), 1)
            query = formatted_query

        response = requests.post(
            f"{self.base_url}/",
            auth=self.auth,
            data=query,
            headers={'Content-Type': 'text/plain'},
            params={'database': self.database, 'default_format': 'JSONEachRow'}
        )
        response.raise_for_status()
        return response

    def execute_query(self, query: str, params: Optional[List[Any]] = None) -> List[Dict[str, Any]]:
        """Execute a SELECT query and return results as list of dictionaries"""
        try:
            response = self._execute_clickhouse_query(query, params)
            results = []
            for line in response.text.strip().split('\n'):
                if line:
                    results.append(json.loads(line))
            return results
        except Exception as e:
            logger.error(f"Database query error: {e}")
            raise

    def execute_single_query(self, query: str, params: Optional[List[Any]] = None) -> Optional[Dict[str, Any]]:
        """Execute a SELECT query and return single result as dictionary"""
        try:
            results = self.execute_query(query, params)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Database single query error: {e}")
            raise

    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            response = self._execute_clickhouse_query("SELECT 1")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

# Global database instance
db = Database()
