#!/usr/bin/env python3
"""
Test script to verify ClickHouse database connection and query execution
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from database import db
from services.clinical_service import ClinicalService

def test_connection():
    """Test basic database connection"""
    print("Testing ClickHouse database connection...")
    try:
        result = db.test_connection()
        if result:
            print("✅ Database connection successful!")
            return True
        else:
            print("❌ Database connection failed!")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_simple_query():
    """Test a simple query"""
    print("\nTesting simple query...")
    try:
        result = db.execute_query("SELECT 1 as test_value")
        print(f"✅ Simple query successful: {result}")
        return True
    except Exception as e:
        print(f"❌ Simple query failed: {e}")
        return False

def test_base_query():
    """Test the base clinical query"""
    print("\nTesting base clinical query...")
    try:
        # Test just the structure with LIMIT 1
        query = f"""
        {ClinicalService.BASE_QUERY}
        LIMIT 1
        """
        result = db.execute_query(query)
        print(f"✅ Base query successful! Returned {len(result)} rows")
        if result:
            print(f"Sample row keys: {list(result[0].keys())}")
        return True
    except Exception as e:
        print(f"❌ Base query failed: {e}")
        return False

def test_doctors_query():
    """Test the doctors query"""
    print("\nTesting doctors query...")
    try:
        doctors = ClinicalService.get_doctors()
        print(f"✅ Doctors query successful! Found {len(doctors)} doctors")
        if doctors:
            print(f"Sample doctor: {doctors[0].name}")
        return True
    except Exception as e:
        print(f"❌ Doctors query failed: {e}")
        return False

def main():
    """Run all tests"""
    print("ClickHouse Database Connection Test")
    print("=" * 40)
    
    tests = [
        test_connection,
        test_simple_query,
        test_base_query,
        test_doctors_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! ClickHouse integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration and database.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
