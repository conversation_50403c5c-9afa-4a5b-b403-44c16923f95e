#!/usr/bin/env python3
"""
Test script for FastAPI endpoints
"""

import requests
import json
from datetime import datetime

BASE_URL = 'http://localhost:8000'

def test_endpoint(endpoint, description):
    """Test a single endpoint"""
    try:
        print(f"\n🧪 Testing {description}")
        print(f"   URL: {BASE_URL}{endpoint}")
        
        response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Success: {response.status_code}")
            if 'data' in data:
                print(f"   📊 Data type: {type(data['data'])}")
                if isinstance(data['data'], list):
                    print(f"   📝 Items count: {len(data['data'])}")
                elif isinstance(data['data'], dict):
                    print(f"   🔑 Keys: {list(data['data'].keys())}")
            return True
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"   🔌 Connection Error: Server not running on {BASE_URL}")
        return False
    except Exception as e:
        print(f"   💥 Error: {e}")
        return False

def main():
    """Test all endpoints"""
    print("🚀 FastAPI Endpoint Testing")
    print("=" * 50)
    
    endpoints = [
        ("/", "Root endpoint"),
        ("/health", "Health check"),
        ("/api/clinical/health", "Clinical health check"),
        ("/api/clinical/doctors", "Doctors list"),
        ("/api/clinical/data?limit=5", "Clinical data (5 records)"),
        ("/api/clinical/statistics", "Statistics"),
        ("/api/clinical/diagnosis-trends", "Diagnosis trends"),
        ("/api/clinical/doctor-activity", "Doctor activity"),
        ("/api/clinical/diagnosis-by-age", "Diagnosis by age"),
        ("/api/clinical/symptom-clusters", "Symptom clusters"),
        ("/api/clinical/patient-demographics", "Patient demographics"),
        ("/api/clinical/quality-metrics", "Quality metrics"),
    ]
    
    results = []
    for endpoint, description in endpoints:
        success = test_endpoint(endpoint, description)
        results.append((endpoint, description, success))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(1 for _, _, success in results if success)
    total = len(results)
    
    for endpoint, description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {description}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! FastAPI backend is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the server and database connection.")

if __name__ == "__main__":
    main()
