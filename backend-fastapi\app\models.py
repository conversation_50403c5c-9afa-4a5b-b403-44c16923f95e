from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

# Enums
class GenderEnum(str, Enum):
    MALE = "Male"
    FEMALE = "Female"
    OTHER = "Other"

class AgeGroupEnum(str, Enum):
    UNDER_16 = "<16"
    AGE_17_25 = "17-25"
    AGE_26_40 = "26-40"
    AGE_41_55 = "41-55"
    AGE_56_65 = "56-65"
    OVER_65 = ">65"

# Request Models
class ClinicalDataQuery(BaseModel):
    page: Optional[int] = Field(default=1, ge=1)
    limit: Optional[int] = Field(default=100, ge=1, le=1000)
    start_date: Optional[str] = Field(default=None, alias="startDate")
    end_date: Optional[str] = Field(default=None, alias="endDate")
    doctor_id: Optional[str] = Field(default=None, alias="doctorId")
    diagnosis: Optional[str] = None
    age_group: Optional[AgeGroupEnum] = Field(default=None, alias="ageGroup")
    gender: Optional[GenderEnum] = None
    unit_id: Optional[str] = Field(default=None, alias="unitId")

class StatisticsQuery(BaseModel):
    start_date: Optional[str] = Field(default=None, alias="startDate")
    end_date: Optional[str] = Field(default=None, alias="endDate")
    doctor_id: Optional[str] = Field(default=None, alias="doctorId")
    unit_id: Optional[str] = Field(default=None, alias="unitId")

# Response Models
class Patient(BaseModel):
    id: str
    age: int
    gender: GenderEnum

class Doctor(BaseModel):
    id: str
    name: str

class Unit(BaseModel):
    id: str
    name: str

class Visit(BaseModel):
    id: str
    patient: Patient
    doctor: Doctor
    date: str
    chief_complaint: str = Field(alias="chiefComplaint")
    diagnosis: str
    medications: List[str]
    investigations: List[str]
    referred: bool

class TopItem(BaseModel):
    name: str
    count: int

class Statistics(BaseModel):
    total_patients: int = Field(alias="totalPatients")
    total_visits: int = Field(alias="totalVisits")
    total_doctors: int = Field(alias="totalDoctors")
    referral_rate: float = Field(alias="referralRate")
    polypharmacy_count: int = Field(alias="polypharmacyCount")
    top_complaint: TopItem = Field(alias="topComplaint")
    top_diagnosis: TopItem = Field(alias="topDiagnosis")
    top_medication: TopItem = Field(alias="topMedication")
    avg_investigations: str = Field(alias="avgInvestigations")
    most_frequent_pair: str = Field(alias="mostFrequentPair")

class DiagnosisTrendDataset(BaseModel):
    label: str
    data: List[int]

class DiagnosisTrends(BaseModel):
    labels: List[str]
    datasets: List[DiagnosisTrendDataset]

class DoctorActivity(BaseModel):
    name: str
    avg_patients: str = Field(alias="avgPatients")
    top_diagnosis: str = Field(alias="topDiagnosis")
    avg_meds: str = Field(alias="avgMeds")

class DiagnosisByAge(BaseModel):
    labels: List[str]
    datasets: List[DiagnosisTrendDataset]

class SymptomCluster(BaseModel):
    cluster_name: str = Field(alias="clusterName")
    symptoms: List[str]
    probable_diagnosis: str = Field(alias="probableDiagnosis")

class ClinicalDataResponse(BaseModel):
    visits: List[Visit]
    doctors: List[Doctor]
    total_count: int = Field(alias="totalCount")
    page: int
    limit: int

class ApiResponse(BaseModel):
    success: bool
    data: Any
    timestamp: str

class HealthCheck(BaseModel):
    status: str
    database: bool
    timestamp: str

class PatientDemographics(BaseModel):
    total_patients: int = Field(alias="totalPatients")
    age_groups: Dict[str, int] = Field(alias="ageGroups")
    gender_distribution: Dict[str, int] = Field(alias="genderDistribution")

class QualityMetrics(BaseModel):
    total_patients: int = Field(alias="totalPatients")
    referral_rate: float = Field(alias="referralRate")
    polypharmacy_count: int = Field(alias="polypharmacyCount")
    avg_medications_per_visit: float = Field(alias="avgMedicationsPerVisit")
    avg_investigations_per_visit: float = Field(alias="avgInvestigationsPerVisit")
    most_common_diagnosis: str = Field(alias="mostCommonDiagnosis")
    most_common_complaint: str = Field(alias="mostCommonComplaint")
