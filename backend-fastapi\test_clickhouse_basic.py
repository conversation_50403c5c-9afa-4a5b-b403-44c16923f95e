#!/usr/bin/env python3
"""
Basic ClickHouse connectivity test
"""

import requests
import json

# ClickHouse connection details
CLICKHOUSE_HOST = "***********"
CLICKHOUSE_PORT = "8123"
CLICKHOUSE_USER = "default"
CLICKHOUSE_PASSWORD = "click123"
CLICKHOUSE_DATABASE = "default"

def test_basic_connectivity():
    """Test basic HTTP connectivity to ClickHouse"""
    print("Testing basic HTTP connectivity...")
    
    url = f"http://{CLICKHOUSE_HOST}:{CLICKHOUSE_PORT}/"
    
    try:
        # Simple ping test
        response = requests.get(url, timeout=10)
        print(f"✅ HTTP connectivity successful! Status: {response.status_code}")
        print(f"Response: {response.text[:100]}")
        return True
    except Exception as e:
        print(f"❌ HTTP connectivity failed: {e}")
        return False

def test_authentication():
    """Test authentication with ClickHouse"""
    print("\nTesting authentication...")
    
    url = f"http://{CLICKHOUSE_HOST}:{CLICKHOUSE_PORT}/"
    auth = (CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)
    
    try:
        response = requests.post(
            url,
            auth=auth,
            data="SELECT 1",
            headers={'Content-Type': 'text/plain'},
            params={'database': CLICKHOUSE_DATABASE},
            timeout=10
        )
        print(f"✅ Authentication successful! Status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        return False

def test_json_format():
    """Test JSON format response"""
    print("\nTesting JSON format...")
    
    url = f"http://{CLICKHOUSE_HOST}:{CLICKHOUSE_PORT}/"
    auth = (CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)
    
    try:
        response = requests.post(
            url,
            auth=auth,
            data="SELECT 1 as test_value",
            headers={'Content-Type': 'text/plain'},
            params={'database': CLICKHOUSE_DATABASE, 'default_format': 'JSONEachRow'},
            timeout=10
        )
        print(f"✅ JSON format successful! Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        # Try to parse JSON
        for line in response.text.strip().split('\n'):
            if line:
                data = json.loads(line)
                print(f"Parsed JSON: {data}")
        return True
    except Exception as e:
        print(f"❌ JSON format failed: {e}")
        return False

def test_database_access():
    """Test database access"""
    print("\nTesting database access...")
    
    url = f"http://{CLICKHOUSE_HOST}:{CLICKHOUSE_PORT}/"
    auth = (CLICKHOUSE_USER, CLICKHOUSE_PASSWORD)
    
    try:
        # Test showing databases
        response = requests.post(
            url,
            auth=auth,
            data="SHOW DATABASES",
            headers={'Content-Type': 'text/plain'},
            timeout=10
        )
        print(f"✅ Database access successful! Status: {response.status_code}")
        print(f"Available databases: {response.text}")
        return True
    except Exception as e:
        print(f"❌ Database access failed: {e}")
        return False

def main():
    """Run all tests"""
    print("ClickHouse Basic Connectivity Test")
    print("=" * 40)
    
    tests = [
        test_basic_connectivity,
        test_authentication,
        test_json_format,
        test_database_access
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All basic connectivity tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Please check ClickHouse server and configuration.")
        return 1

if __name__ == "__main__":
    exit(main())
